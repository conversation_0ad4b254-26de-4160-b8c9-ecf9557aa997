import React from "react";
import { ResponseType } from "../addQuestions/util/constant";
import MultipleC<PERSON>iceViewer from "./MultipleChoiceViewer";
import ScaleViewer from "./ScaleViewer";
import <PERSON>NoViewer from "./YesNoViewer";
import <PERSON>hum<PERSON>Viewer from "./ThumbsViewer";
import EmojiViewer from "./EmojiViewer";
import RatingViewer from "./RatingViewer";
import NpsViewer from "./NpsViewer";

interface ResponseViewerProps {
  responseType: ResponseType;
}

const ResponseViewer: React.FC<ResponseViewerProps> = ({ responseType }) => {
  const renderView = () => {
    switch (responseType) {
      case ResponseType.MULTIPLE_CHOICE:
        return <MultipleChoiceViewer />;
      case ResponseType.SCALE:
        return <ScaleViewer />;
      case ResponseType.YES_NO:
        return <YesNoViewer />;
      case ResponseType.THUMBS_UP_DOWN:
        return <ThumbsViewer />;
      case ResponseType.EMOJI:
        return <EmojiViewer />;
      case ResponseType.RATING:
        return <RatingViewer />;
      case ResponseType.NET_PROMOTER_SCORE:
        return <NpsViewer />;
      case ResponseType.COMMENT:
        return null; // No additional UI needed for comment type
      default:
        return null;
    }
  };

  return <div>{renderView()}</div>;
};

export default ResponseViewer;
