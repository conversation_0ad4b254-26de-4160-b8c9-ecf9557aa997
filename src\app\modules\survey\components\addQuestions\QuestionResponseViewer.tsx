import React, { useMemo, useState } from "react";
import {
  emojiOptions,
  QuestionFormValues,
  ResponseType,
  thumbsOptions,
} from "./util/constant";
import { BranchingFormValues } from "./hooks/useBranchingForm";
import QuestionResponseWrapper from "./QuestionResponseWrapper";
import MultipleChoiceResponse from "../response/MultipleChoiceResponse";
import ScaleResponse from "../response/ScaleResponse";
import YesNoResponse from "../response/YesNoResponse";
import ThumbsUpDownResponse from "../response/ThumbsUpDownResponse";
import EmojiResponse from "../response/EmojiResponse";
import RatingResponse from "../response/RatingResponse";
import NpsResponse from "../response/NpsResponse";
import CommentResponse from "../response/CommentResponse";
import { ISurveyQuestionListItem } from "../../../../apis/type";

interface SurveyAnswer {
  answers: string[];
  comment: string;
  attachments: Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
  }>;
  branchingAnswer?: {
    answers: string[];
    comment: string;
    attachments: Array<{
      attachmentId: string;
      attachmentUrl: string;
      attachmentType: string;
    }>;
  };
}

interface Props {
  question: ISurveyQuestionListItem;
  readOnly?: boolean;
  questionNumber?: number;
  onAnswerChange?: (answer: SurveyAnswer) => void;
  initialAnswer?: SurveyAnswer;
}

const QuestionResponseViewer: React.FC<Props> = ({
  question,
  questionNumber,
  readOnly,
  onAnswerChange,
  initialAnswer,
}) => {
  const options = useMemo(() => {
    return (question.options || []).map((opt) => ({
      label: typeof opt === 'string' ? opt : opt.optionText,
      value: typeof opt === 'string' ? opt : opt.optionValue,
    }));
  }, [question.options]);

  // Derive scale values from options for SCALE response type
  const scaleValues = useMemo(() => {
    if (question.responseType === ResponseType.SCALE && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val))
        .sort((a, b) => a - b);

      return {
        startScale: numericValues[0] || 0,
        endScale: numericValues[numericValues.length - 1] || 5
      };
    }
    // Default scale values if no options available
    return { startScale: 0, endScale: 5 };
  }, [question.options, question.responseType]);

  // Derive rating values from options for RATING response type
  const ratingValues = useMemo(() => {
    if (question.responseType === ResponseType.RATING && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val));

      return {
        ratting: Math.max(...numericValues) || 5
      };
    }
    // Default rating value if no options available
    return { ratting: 5 };
  }, [question.options, question.responseType]);

  // Derive NPS values from options for NET_PROMOTER_SCORE response type
  const npsValues = useMemo(() => {
    if (question.responseType === ResponseType.NET_PROMOTER_SCORE && question.options && question.options.length > 0) {
      const numericValues = question.options
        .map(opt => typeof opt === 'string' ? parseInt(opt) : parseInt(opt.optionValue))
        .filter(val => !isNaN(val))
        .sort((a, b) => a - b);

      return {
        startNetPromoterScore: numericValues[0] || 0,
        endNetPromoterScore: numericValues[numericValues.length - 1] || 10
      };
    }
    // Default NPS range if no options available
    return {
      startNetPromoterScore: 0,
      endNetPromoterScore: 10
    };
  }, [question.options, question.responseType]);

  // Local state for value - initialize with initial answer if provided
  const [responseValue, setResponseValue] = useState<string | number>(
    initialAnswer?.answers?.[0] || ""
  );

  // Local state for comment
  const [comment, setComment] = useState<string>(
    initialAnswer?.comment || ""
  );

  // Local state for attachments
  const [attachments, setAttachments] = useState<Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>>(
    initialAnswer?.attachments?.map(att => ({
      attachmentId: att.attachmentId,
      attachmentUrl: att.attachmentUrl,
      attachmentType: att.attachmentType,
    })) || []
  );

  const [branchingAttachments, setBranchingAttachments] = useState<Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>>(
    initialAnswer?.branchingAnswer?.attachments?.map(att => ({
      attachmentId: att.attachmentId,
      attachmentUrl: att.attachmentUrl,
      attachmentType: att.attachmentType,
    })) || []
  );

  // Notify parent of answer changes
  React.useEffect(() => {
    if (onAnswerChange) {
      const answer: SurveyAnswer = {
        answers: responseValue ? [responseValue.toString()] : [],
        comment,
        attachments: attachments.map(att => ({
          attachmentId: att.attachmentId,
          attachmentUrl: att.attachmentUrl,
          attachmentType: att.attachmentType,
        })),
        branchingAnswer: question.allowBranching ? {
          answers: [], // TODO: Handle branching answers
          comment: '', // TODO: Handle branching comment
          attachments: branchingAttachments.map(att => ({
            attachmentId: att.attachmentId,
            attachmentUrl: att.attachmentUrl,
            attachmentType: att.attachmentType,
          })),
        } : undefined,
      };
      onAnswerChange(answer);
    }
  }, [responseValue, comment, attachments, branchingAttachments, onAnswerChange, question.allowBranching]);

  const renderView = () => {
    switch (question.responseType) {
      case ResponseType.MULTIPLE_CHOICE:
        return (
          <MultipleChoiceResponse
            options={options}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={setResponseValue}
          />
        );
      case ResponseType.SCALE:
        return (
          <ScaleResponse
            startScale={scaleValues.startScale}
            endScale={scaleValues.endScale}
            value={
              typeof responseValue === "number" ? responseValue : undefined
            }
            onChange={setResponseValue}
          />
        );
      case ResponseType.YES_NO:
        return (
          <YesNoResponse
            options={options}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={setResponseValue}
          />
        );
      case ResponseType.THUMBS_UP_DOWN:
        return (
          <ThumbsUpDownResponse
            options={thumbsOptions}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={setResponseValue}
          />
        );
      case ResponseType.EMOJI:
        return (
          <EmojiResponse
            options={emojiOptions}
            value={typeof responseValue === "string" ? responseValue : ""}
            onChange={setResponseValue}
          />
        );
      case ResponseType.RATING:
        return (
          <RatingResponse
            ratting={ratingValues.ratting}
            rattingIcon={question.rattingIcon as "smiley" | "star" | "thumb_up"}
            value={typeof responseValue === "number" ? responseValue : 0}
            onChange={setResponseValue}
          />
        );
      case ResponseType.NET_PROMOTER_SCORE:
        return (
          <NpsResponse
            startNetPromoterScore={npsValues.startNetPromoterScore}
            endNetPromoterScore={npsValues.endNetPromoterScore}
            value={typeof responseValue === "number" ? responseValue : 0}
            onChange={setResponseValue}
          />
        );
      case ResponseType.COMMENT:
        return (
          <CommentResponse
            value={comment}
            onChange={setComment}
          />
        );
      default:
        return <></>;
    }
  };

  return (
    <QuestionResponseWrapper
      questionText={question.questionText}
      isRequired={question.isRequired}
      allowAttachment={question.allowAttachment}
      allowedAttachmentType={question.attachmentType}
      attachments={attachments}
      onAttachmentsChange={setAttachments}
      allowComment={question.allowComment}
      allowBranching={question.allowBranching}
      branchingQuestionText={question?.branchingQuestion?.questionText} // <-- updated property name
      allowbranchingAttchement={question.branchingQuestion?.allowAttachment}
      branchingAttchementType={question.branchingQuestion?.attachmentType}
      branchingAttachments={branchingAttachments}
      onBranchingAttachmentsChange={setBranchingAttachments}
      questionNumber={questionNumber}
      readOnly={readOnly}
      id={question.id}
    >
      {renderView()}
    </QuestionResponseWrapper>
  );
};

export default QuestionResponseViewer;