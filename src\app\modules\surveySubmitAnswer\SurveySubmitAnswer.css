.survey-submit-answer-page {
  min-height: 100vh;
  /* background-color: #f8f9fa; */
  padding: 20px 0;
}

.survey-welcome {
  min-height: 100vh;
  display: flex;
  align-items: center;
  /* background-color: #f8f9fa; */
}

.survey-answer-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
}

.survey-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.survey-question-step {
  margin-bottom: 2rem;
}

.survey-navigation {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
}

.question-counter {
  font-size: 1.1rem;
  color: #6c757d;
}

.progress {
  height: 8px;
  border-radius: 4px;
}

.progress-bar {
  background-color: #007bff;
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .survey-submit-answer-page {
    padding: 10px 0;
  }
  
  .survey-navigation .d-flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .survey-navigation .question-counter {
    order: -1;
    text-align: center;
  }
}
