import React from 'react';
import { useFormikContext } from 'formik';
import { QuestionFormValues, generateThumbsOptions } from '../addQuestions/util/constant';
import { LuThumbsUp, LuThumbsDown } from 'react-icons/lu';

const ThumbsViewer: React.FC = () => {
  const { setFieldValue } = useFormikContext<QuestionFormValues>();

  // Set default options when component mounts
  React.useEffect(() => {
    const thumbsOptions = generateThumbsOptions();
    setFieldValue('options', thumbsOptions);
  }, [setFieldValue]);

  return (
    <div className="thumbs-viewer mt-5">
      <div className="gap-4">
        <div className="response-viewer-box-wrapper d-flex align-items-center gap-2 mb-4">
          <div className="">
            <LuThumbsUp size={20} />
          </div>
          <span>Thumbs Up</span>
        </div>
        <div className="response-viewer-box-wrapper d-flex align-items-center gap-2">
          <div className="">
            <LuThumbsDown size={20} />
          </div>
          <span>Thumbs Down</span>
        </div>
      </div>
    </div>
  );
};

export default ThumbsViewer; 