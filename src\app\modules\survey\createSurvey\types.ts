import { BranchingFormValues } from "../components/addQuestions/hooks/useBranchingForm";
import { QuestionFormValues } from "../components/addQuestions/util/constant";

export interface SurveyFormValues {
  // General Setup Fields
  surveyImage: string | null; // S3 URL string from upload
  surveyName: string;
  surveyWelcomeMessage: string;
  propertyIds: string[] | string; // Array for property users, string for department users
  departmentIds: string[];
  surveyGoesTo: string[];
  surveyClosingMessage: boolean;
  closingMessage: string; // The actual closing message when surveyClosingMessage is true
  redirectionUrl: boolean;
  redirectUrl: string; // The actual redirect URL when redirectionUrl is true

  // Question Setup Fields (for future steps)
  questions?: any[];

  // Configuration Fields (for future steps)
  configuration?: any;
}

export interface PropertyOption {
  value: string;
  label: string;
}

export interface DepartmentOption {
  value: string;
  label: string;
}

export interface UserOption {
  value: string;
  label: string;
}


export type QuestionType = QuestionFormValues & Partial<BranchingFormValues>;
