import React from 'react'

interface CommentResponseProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

const CommentResponse: React.FC<CommentResponseProps> = ({
  value = '',
  onChange,
  placeholder = 'Enter your comment...'
}) => {
  return (
    <textarea
      className='form-control'
      value={value}
      onChange={(e) => onChange?.(e.target.value)}
      placeholder={placeholder}
      rows={3}
    />
  )
}

export default CommentResponse