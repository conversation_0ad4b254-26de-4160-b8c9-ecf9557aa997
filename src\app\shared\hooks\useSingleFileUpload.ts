import { useState, useCallback } from "react";
import axiosInstance from "../../serverconfig/axiosInstance";
import { APIs } from "../../serverconfig/apiURLs";
import { getDecryptData } from "../../utils/CommonUtils";

interface SurveyImageUpload {
  id: string;
  url: string;
  name?: string;
  previewUrl?: string;
  isUploading?: boolean;
}

export const useSingleFileUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<SurveyImageUpload | null>(null);
  const [error, setError] = useState<string>("");

  const validateImage = useCallback((file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      // Check file size (3MB max)
      if (file.size > 3 * 1024 * 1024) {
        setError("File size should be less than 3MB");
        resolve(false);
        return;
      }

      // Check file type
      if (!file.type.startsWith("image/")) {
        setError("Only image files are allowed");
        resolve(false);
        return;
      }

      // Check image dimensions (1600px min width)
      const img = new Image();
      img.onload = () => {
        if (img.width < 1600) {
          setError("Image width should be at least 1600px");
          resolve(false);
          return;
        }
        setError("");
        resolve(true);
      };
      img.onerror = () => {
        setError("Invalid image file");
        resolve(false);
      };
      img.src = URL.createObjectURL(file);
    });
  }, []);

  const uploadImage = useCallback(
    async (file: File): Promise<SurveyImageUpload | null> => {
      setIsUploading(true);
      setError("");

      // Validate image first
      // const isValid = await validateImage(file);
      // if (!isValid) {
      //   setIsUploading(false);
      //   return null;
      // }

      const previewUrl = URL.createObjectURL(file);

      // Set temporary preview while uploading
      const tempImage: SurveyImageUpload = {
        id: "",
        url: "",
        name: file.name,
        previewUrl,
        isUploading: true,
      };
      setUploadedImage(tempImage);

      try {
        const formData = new FormData();
        formData.append("files", file);

        const response = await axiosInstance.post(
          APIs.MULTIPART.FILE_UPLOAD,
          formData
        );
        const resData = response?.data;

        if (resData) {
          const decryptedData = getDecryptData(resData.data);

          const uploadedImage: SurveyImageUpload = {
            id: decryptedData?.id || "",
            url: decryptedData?.documenturl || "",
            name: file.name,
            previewUrl,
            isUploading: false,
          };

          setUploadedImage(uploadedImage);
          return uploadedImage;
        }
      } catch (error: any) {
        console.error(`Survey image upload failed:`, error);
        setError(error?.response?.data?.message || "Upload failed. Please try again.");
        setUploadedImage(null);
      } finally {
        setIsUploading(false);
      }

      return null;
    },
    [validateImage]
  );

  const removeImage = useCallback(() => {
    if (uploadedImage?.previewUrl) {
      URL.revokeObjectURL(uploadedImage.previewUrl);
    }
    setUploadedImage(null);
    setError("");
  }, [uploadedImage]);

  const clearError = useCallback(() => {
    setError("");
  }, []);

  return {
    isUploading,
    uploadedImage,
    error,
    uploadImage,
    removeImage,
    clearError,
  };
};
