import React, { useEffect, useState } from 'react';
import { useFormikContext } from 'formik';
import { QuestionFormValues, generateScaleOptions } from '../addQuestions/util/constant';
import FormSelect from '../../../../component/form/FormSelect';
import FormLabel from '../../../../component/form/FormLabel';

const ScaleViewer: React.FC = () => {
  const { setFieldValue } = useFormikContext<QuestionFormValues>();

  // Internal state for scale values
  const [startScale, setStartScale] = useState<number>(0);
  const [endScale, setEndScale] = useState<number>(5);

  const startScaleOptions = [
    { label: '0', value: 0 },
    { label: '1', value: 1 }
  ];

  const endScaleOptions = Array.from({ length: 6 }, (_, i) => ({
    label: String(i + 5),
    value: i + 5
  }));

  // Generate options when scale values change
  useEffect(() => {
    if (startScale !== undefined && endScale !== undefined && startScale <= endScale) {
      const scaleOptions = generateScaleOptions(startScale, endScale);
      setFieldValue('options', scaleOptions);
    }
  }, [startScale, endScale, setFieldValue]);

  return (
    <div className="scale-viewer">
      <div className="row">
        <div className="col-md-6 mb-3">
          <FormLabel className="form-label">Start Scale</FormLabel>
          <FormSelect
            name="startScale"
            options={startScaleOptions}
            placeholder="Select start scale"
            value={startScale}
            onChange={(selectedOption: any) => setStartScale(selectedOption?.value || 0)}
          />
        </div>
        <div className="col-md-6 mb-3">
          <FormLabel className="form-label">End Scale</FormLabel>
          <FormSelect
            name="endScale"
            options={endScaleOptions}
            placeholder="Select end scale"
            value={endScale}
            onChange={(selectedOption: any) => setEndScale(selectedOption?.value || 5)}
          />
        </div>
      </div>
    </div>
  );
};

export default ScaleViewer; 