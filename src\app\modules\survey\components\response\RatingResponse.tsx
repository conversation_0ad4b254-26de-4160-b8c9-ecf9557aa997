import React, { useState, useEffect } from 'react';
import { LuSmile, LuStar, LuThumbsUp } from 'react-icons/lu';

interface RatingResponseProps {
  ratting: number;
  rattingIcon: 'smiley' | 'star' | 'thumb_up';
  value: number;
  onChange: (value: number) => void;
}

const iconMap = {
  smiley: LuSmile,
  star: LuStar,
  thumb_up: LuThumbsUp,
};

const RatingResponse: React.FC<RatingResponseProps> = ({ ratting, rattingIcon, value, onChange }) => {
  const [selected, setSelected] = useState(value);
  const Icon = iconMap[rattingIcon];

  useEffect(() => {
    setSelected(value);
  }, [value]);

  const handleSelect = (val: number) => {
    setSelected(val);
    onChange(val);
  };

  return (
    <div style={{ display: 'flex', gap: 8 }}>
      {Array.from({ length: ratting }, (_, i) => (
        <span
          key={i}
          onClick={() => handleSelect(i + 1)}
          style={{
            cursor: 'pointer',
            color: i < selected ? '#FFD700' : '#ccc',
            fontSize: 32,
            transition: 'color 0.2s',
          }}
        >
          <Icon fill={i < selected ? '#FFD700' : 'none'} />
        </span>
      ))}
    </div>
  );
};

export default RatingResponse; 