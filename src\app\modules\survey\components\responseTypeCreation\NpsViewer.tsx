import React, { useEffect, useState } from 'react';
import { useFormikContext } from 'formik';
import { QuestionFormValues, generateNpsOptions } from '../addQuestions/util/constant';

const NpsViewer: React.FC = () => {
  const { setFieldValue } = useFormikContext<QuestionFormValues>();

  // Internal state for NPS range (standard NPS is always 0-10)
  const [startNetPromoterScore] = useState<number>(0);
  const [endNetPromoterScore] = useState<number>(10);

  // Generate options when component mounts
  useEffect(() => {
    // Generate NPS options based on internal state
    const npsOptions = generateNpsOptions(startNetPromoterScore, endNetPromoterScore);
    setFieldValue('options', npsOptions);
  }, [startNetPromoterScore, endNetPromoterScore, setFieldValue]);

  return (
    <div className="nps-viewer mt-5">
      <div className="d-flex align-items-center justify-content-between">
        <div className="text-center response-viewer-box-wrapper">
          <div className="fs-2 mb-2">{startNetPromoterScore}</div>
          <span>Not at all likely</span>
        </div>
        <div className="text-center response-viewer-box-wrapper">
          <div className="fs-2 mb-2">{endNetPromoterScore}</div>
          <span>Extremely likely</span>
        </div>
      </div>
    </div>
  );
};

export default NpsViewer; 