import { Form, Formik } from "formik";
import { useLocation, useNavigate } from "react-router-dom";
import React, { useEffect } from "react";
import { SurveyFormValues } from "./types";
import { useGeneralSetup } from "./useGeneralSetup";
import GeneralSetup from "./GeneralSetup";
import Footer from "./Footer";
import {
  useGeneralSetupMutation,
  useGetSurveyFullDetailsMutation,
} from "../../../apis/survaysAPI";
import { Loader } from "../../../component";
import { processApiResponse } from "../../../utils/helper";
import useSurveyUtil from "../helper/useDetectSurvayType";
import SwalMessage from "../../common/SwalMessage";

interface Props {
  handleNext: () => void;
  handleBack: () => void;
}

const GeneralSetupFormWrapper: React.FC<Props> = ({
  handleNext,
  handleBack,
}) => {
  const permissiontype = JSON.parse(
    localStorage.getItem("userdetail") as string
  )?.permission;
  const isPropertyBaseUser = permissiontype !== "D";

  const {
    initialValues,
    setInitialValues,
    generalSetupSchema,
    convertToApiPayload,
  } = useGeneralSetup();
  const [generalSetup, { isLoading }] = useGeneralSetupMutation();
  const [getSurveyFullDetails, { isLoading: isFetchingFullDetails }] =
    useGetSurveyFullDetailsMutation();
  const location = useLocation();
  const navigate = useNavigate();
  const { surveyId, surveyType } = useSurveyUtil();
  const isMultiProperSelection = surveyType === "GLOBAL" && isPropertyBaseUser;

  useEffect(() => {
    if (surveyId) {
      getSurveyFullDetails({
        surveyId,
      })
        .unwrap()
        .then((res) => {
          if (res?.data) {
            setInitialValues(() => ({
              propertyIds: isMultiProperSelection
                ? res.data.propertyDetailsResponses?.map(
                    (item) => item.propertyId
                  ) || []
                : res.data.propertyDetailsResponses?.[0]?.propertyId || "",
              departmentIds:
                res.data.departmentDetailsResponses?.map(
                  (item) => item.departmentId
                ) || [],
              surveyGoesTo:
                res.data.surveyTarget?.map((item) => item.userId) || [],
              surveyClosingMessage: !!res.data.closingMessage || false,
              closingMessage: res.data.closingMessage || "",
              redirectionUrl: !!res.data.redirectUrl || false,
              redirectUrl: res.data.redirectUrl || "",
              surveyImage: res.data.surveyImage || "",
              surveyName: res.data.surveyName || "",  
              surveyWelcomeMessage: res.data.welcomeMessage || "",
            }));
          }
        })
        .catch(() => {
          SwalMessage(null, "Something went wrong", "Ok", "error", false);
        });
    }
  }, [surveyId]);

  const handleSubmit = async (values: SurveyFormValues) => {
    // Convert form values to API payload format
    const apiPayload = convertToApiPayload(values);
    const res = await generalSetup(apiPayload).unwrap();
    processApiResponse({
      res,
      onSuccess: (res) => {
        const id = res.data?.surveyId;
        if (id) {
          // Set id into current router state using react-router-dom
          navigate(location.pathname + location.search, {
            replace: true,
            state: { ...(location.state || {}), id },
          });
        }
        handleNext();
      },
    });
  };

  return (
    <>
      {isLoading || (isFetchingFullDetails && <Loader />)}
      <Formik
        initialValues={initialValues}
        validationSchema={generalSetupSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {(formik) => (
          <Form className="mt-4">
            <GeneralSetup
              formik={formik}
              isPropertyBaseUser={isPropertyBaseUser}
              isMultiProperSelection={
                surveyType === "GLOBAL" && isPropertyBaseUser
              }
            />
            <div className="row save-continue-footer">
              <div className="row custom-card d-flex justify-content-end border-0">
                <Footer handleNext={formik.handleSubmit} />
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default GeneralSetupFormWrapper;
