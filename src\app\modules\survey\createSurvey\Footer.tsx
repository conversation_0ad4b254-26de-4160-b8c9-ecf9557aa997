import React from "react";

interface Props {
  handleNext: () => void;
  handleBack?: () => void;
  saveLabel?: string;
}

const Footer: React.FC<Props> = ({
  handleBack,
  handleNext,
  saveLabel,
}) => {
  return (
    <div className="d-flex justify-content-between py-2">
      <div>
        {!!handleBack && (
          <button
            type="button"
            className="btn btn-outline-secondary"
            onClick={handleBack}
          >
            Go Back
          </button>
        )}
      </div>
      <span
        className="btn rx-btn cursor-pointer d-inline-flex align-items-center justify-content-center px-5"
        role="button"
        onClick={handleNext}
      >
        {saveLabel || "Next"}
      </span>
    </div>
  );
};

export default Footer;
