import { useEffect, useMemo, useState } from "react";
import * as Yup from "yup";
import { SurveyFormValues } from "./types";
import { useSearchParams } from "react-router-dom";
import { ISurveyGeneralSetupPayload, SurveyType } from "../../../apis/type";
import useSurveyUtil from "../helper/useDetectSurvayType";

export const useGeneralSetup = () => {
  const [searchParams] = useSearchParams();
  const { surveyType, surveyId } = useSurveyUtil();

  // Common function to get initial survey values
  function getInitialSurveyValues(
    isPropertyBaseUser: boolean,
    surveyType: SurveyType
  ): SurveyFormValues {
    return {
      surveyImage: "",
      surveyName: "",
      surveyWelcomeMessage: "",
      propertyIds: isPropertyBaseUser && surveyType === "GLOBAL" ? [] : "",
      departmentIds: [],
      surveyGoesTo: [],
      surveyClosingMessage: false,
      closingMessage: "",
      redirectionUrl: false,
      redirectUrl: "",
    };
  }

  // Get user permission to determine form structure
  const userPermission = JSON.parse(
    localStorage.getItem("userdetail") as string
  )?.permission;
  const isPropertyBaseUser = userPermission !== "D";

  const [initialValues, setInitialValues] = useState<SurveyFormValues>(
    getInitialSurveyValues(isPropertyBaseUser, surveyType)
  );

  useEffect(() => {
    setInitialValues(getInitialSurveyValues(isPropertyBaseUser, surveyType));
  }, [isPropertyBaseUser, surveyType]);

  // Validation schema for General Setup (Step 1)
  const generalSetupSchema = useMemo(() => {
    return Yup.object().shape({
      surveyName: Yup.string().required("Survey name is required"),
      // .min(3, "Survey name must be at least 3 characters")
      // .max(100, "Survey name must not exceed 100 characters"),

      surveyWelcomeMessage: Yup.string().required(
        "Survey welcome message is required"
      ),
      // .min(10, "Welcome message must be at least 10 characters")
      // .max(500, "Welcome message must not exceed 500 characters"),

      propertyIds:
        isPropertyBaseUser && surveyType === "GLOBAL"
          ? Yup.array()
              .min(1, "At least one property is required")
              .required("Property is required")
          : Yup.string().required("Property is required"),

      departmentIds: isPropertyBaseUser
        ? Yup.mixed().notRequired()
        : Yup.array()
            .min(1, "At least one department is required")
            .required("Department is required"),

      surveyGoesTo: Yup.array()
        .min(1, "At least one User is required")
        .required("User is required"),

      surveyImage: Yup.string().nullable().url("Invalid image URL"),

      closingMessage: Yup.string().when("surveyClosingMessage", {
        is: true,
        then: (schema) =>
          schema.required(
            "Closing message is required when closing message is enabled"
          ),
        // .min(10, "Closing message must be at least 10 characters")
        // .max(500, "Closing message must not exceed 500 characters"),
        otherwise: (schema) => schema.notRequired(),
      }),

      redirectUrl: Yup.string().when("redirectionUrl", {
        is: true,
        then: (schema) =>
          schema
            .required("Redirect URL is required when redirection is enabled")
            .url("Please enter a valid URL"),
        otherwise: (schema) => schema.notRequired(),
      }),
    });
  }, [isPropertyBaseUser]);

  // Helper function to convert form values to API payload
  const convertToApiPayload = (
    values: SurveyFormValues
  ): ISurveyGeneralSetupPayload => {
    return {
      // "id": "string", //TODO:Pass on edit
      surveyName: values.surveyName,
      surveyImage: values.surveyImage || "",
      welcomeMessage: values.surveyWelcomeMessage,
      surveyPropertyIds: Array.isArray(values.propertyIds)
        ? values.propertyIds
        : [values.propertyIds],
      surveyDepartmentIds: values.departmentIds,
      surveyTarget: values.surveyGoesTo,
      closingMessage: values.closingMessage,
      redirectUrl: values.redirectUrl,
      isDraft: true,
      surveyType: surveyType,
      id: surveyId || null
    };
  };

  return {
    initialValues,
    generalSetupSchema,
    convertToApiPayload,
    isPropertyBaseUser,
    setInitialValues,
  };
};
