import React from 'react';
import FormSelect from '../../../../component/form/FormSelect';

interface MultipleChoiceResponseProps {
  options: { label: string; value: string }[];
  value?: string;
  readOnly?: boolean;
  onChange?: (value: string | number) => void;
}

const MultipleChoiceResponse: React.FC<MultipleChoiceResponseProps> = ({
  options,
  value,
  onChange,
}) => {
  return (
    <FormSelect
      name="multipleChoice"
      options={options}
      value={value}
      isDisabled={false}
      placeholder="Select an option"
      onChange={(selectedOption: any) => {
        if (onChange && selectedOption) {
          onChange(selectedOption.value);
        }
      }}
    />
  );
};

export default MultipleChoiceResponse; 