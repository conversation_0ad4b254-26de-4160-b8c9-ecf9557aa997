import React from "react";
import { Card } from "react-bootstrap";
import { FaExternalLinkAlt } from "react-icons/fa";
import { useNavigate } from "react-router";

const SurveyTypeSelection = () => {
  const navigate = useNavigate()
  return (
    <Card className="custom-card">
      <Card.Body>
        <h1>Create your first survey</h1>
        <div
          className="p-4 rounded d-flex justify-content-between my-5"
          style={{ background: "var(--Rx-bg)" }}
        >
          <div>
            <p className="text-muted mb-3">Create a survey for 1 property</p>
            <h3 className="m-0">Create a LOCAL survey</h3>
          </div>
          <div className="survey-redirection-box custom-card" role="button" onClick={() => navigate("/surveys/survey-create?surveyType=local")}>
            <FaExternalLinkAlt size={20}/>
          </div>
        </div>
        <div className="p-4 rounded d-flex justify-content-between" style={{ background: "var(--Rx-bg)" }}>
          <div>
            <p className="text-muted mb-3">
              Create a survey for multiple properties
            </p>
            <h3 className="m-0">Create a GLOBAL survey</h3>
          </div>
          <div className="survey-redirection-box custom-card" role="button" onClick={() => navigate("/surveys/survey-create?surveyType=global")}>
            <FaExternalLinkAlt size={20}/>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

export default SurveyTypeSelection;
