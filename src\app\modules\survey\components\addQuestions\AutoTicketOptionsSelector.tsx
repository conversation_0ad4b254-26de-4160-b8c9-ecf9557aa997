import React, { useEffect } from 'react';
import { useFormikContext } from 'formik';
import { QuestionFormValues, ResponseType, emojiOptions } from './util/constant';
import { QuestionOptions } from '../../../../apis/type';
import FormLabel from '../../../../component/form/FormLabel';

const AutoTicketOptionsSelector: React.FC = () => {
  const { values, setFieldValue } = useFormikContext<QuestionFormValues>();

  // Don't show if autoTicketGeneration is false or no options available
  if (!values.autoTicketGeneration || !values.options || values.options.length === 0) {
    return null;
  }

  const handleOptionToggle = (optionIndex: number) => {
    const currentOptions = [...(values.options || [])];
    const currentOption = currentOptions[optionIndex];
    
    if (typeof currentOption === 'string') {
      // <PERSON>le legacy string format - convert to QuestionOptions
      currentOptions[optionIndex] = {
        optionText: currentOption,
        optionValue: currentOption,
        isTicketRequired: true,
      };
    } else {
      // Handle QuestionOptions format - toggle isTicketRequired
      currentOptions[optionIndex] = {
        ...currentOption,
        isTicketRequired: !currentOption.isTicketRequired,
      };
    }
    
    setFieldValue('options', currentOptions);
  };

  const getOptionDisplay = (option: string | QuestionOptions, index: number) => {
    const optionValue = typeof option === 'string' ? option : option.optionValue;
    const optionText = typeof option === 'string' ? option : option.optionText;

    switch (values.responseType) {
      case ResponseType.SCALE:
      case ResponseType.RATING:
      case ResponseType.NET_PROMOTER_SCORE:
        // Show numeric values
        return optionValue;

      case ResponseType.EMOJI:
        // Show emoji icon
        const emojiOption = emojiOptions.find(emoji => emoji.value === optionValue);
        return emojiOption ? emojiOption.icon : optionText;

      case ResponseType.YES_NO:
      case ResponseType.THUMBS_UP_DOWN:
      case ResponseType.MULTIPLE_CHOICE:
      default:
        // Show text
        return optionText;
    }
  };

  const isOptionSelected = (option: string | QuestionOptions) => {
    if (typeof option === 'string') {
      return false; // Legacy format doesn't have isTicketRequired
    }
    return option.isTicketRequired || false;
  };

  return (
    <div className="auto-ticket-options-selector" style={{ marginTop: '-10px' }}>
      <FormLabel className="form-label text-primary">
        <i className="fas fa-ticket-alt me-2"></i>
        Select options that should automatically generate tickets:
      </FormLabel>
      <div className="border rounded p-3 mt-2">
        {values.options.length > 0 ? (
          <div className="row gap-2">
            {values.options.map((option, index) => {
              // For non-multiple choice, show 5 per line; for multiple choice, show 3 per line
              const colClass = values.responseType === ResponseType.MULTIPLE_CHOICE
                ? "col-md-6 col-lg-4"
                : "col";

              return (
                <div key={index} className={`${colClass}`}>
                  <div className="d-flex align-items-center gap-3">
                    <input
                      className=""
                      type="checkbox"
                      id={`ticket-option-${index}`}
                      checked={isOptionSelected(option)}
                      onChange={() => handleOptionToggle(index)}
                    />
                    <label
                      className="form-check-label d-flex align-items-center gap-2 user-select-none"
                      htmlFor={`ticket-option-${index}`}
                      style={{ cursor: 'pointer' }}
                    >
                      <span
                        className="fw-medium"
                        style={{
                          fontSize: values.responseType === ResponseType.EMOJI ? '20px' : 'inherit',
                          minWidth: values.responseType === ResponseType.EMOJI ? '24px' : 'auto'
                        }}
                      >
                        {getOptionDisplay(option, index)}
                      </span>
                    </label>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-muted text-center py-3">
            <i className="fas fa-info-circle me-2"></i>
            No options available. Please configure the response type first.
          </div>
        )}
      </div>
    </div>
  );
};

export default AutoTicketOptionsSelector;
