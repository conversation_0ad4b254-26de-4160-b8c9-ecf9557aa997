import React from 'react';
import { useFormikContext } from 'formik';
import { emojiOptions, QuestionFormValues, generateEmojiOptions } from '../addQuestions/util/constant';
import { LuS<PERSON>, LuRocket, LuFlower } from 'react-icons/lu';
import FormErrorMessage from '../../../../component/form/FormErrorMessage';

const EmojiViewer: React.FC = () => {
  const { values, setFieldValue } = useFormikContext<QuestionFormValues>();

  // Set default options when component mounts
  React.useEffect(() => {
    const defaultOptions = generateEmojiOptions();
    setFieldValue('options', defaultOptions);
  }, []);

  const handleEmojiToggle = (emojiValue: string) => {
    const currentOptions = values.options || [];
    const isCurrentlySelected = currentOptions.some(opt =>
      typeof opt === 'string' ? opt === emojiValue : opt.optionValue === emojiValue
    );

    if (isCurrentlySelected) {
      // Remove the emoji option
      const newOptions = currentOptions.filter(opt =>
        typeof opt === 'string' ? opt !== emojiValue : opt.optionValue !== emojiValue
      );
      setFieldValue('options', newOptions);
    } else {
      // Add the emoji option
      const emojiOption = emojiOptions.find(emoji => emoji.value === emojiValue);
      if (emojiOption) {
        const newOption = {
          optionText: emojiOption.label,
          optionValue: emojiOption.value,
          isTicketRequired: false,
        };
        setFieldValue('options', [...currentOptions, newOption]);
      }
    }
  };

  return (
    <div className="emoji-viewer mt-5">
      <div className="d-flex flex-column gap-3">
        {emojiOptions.map(({ value, icon, label }) => {
          const isChecked = values.options?.some(opt =>
            typeof opt === 'string' ? opt === value : opt.optionValue === value
          ) || false;

          return (
            <div key={value} className="response-viewer-box-wrapper">
              <div className="d-flex align-items-center gap-4">
                <input
                  type="checkbox"
                  id={value}
                  checked={isChecked}
                  onChange={() => handleEmojiToggle(value)}
                />
                <label className="d-flex align-items-center gap-2" htmlFor={value}>
                  <div className="" style={{fontSize: 20}}>{icon}</div>
                  {/* <span>{label}</span> */}
                </label>
              </div>
            </div>
          );
        })}
      </div>
      <FormErrorMessage name="options" />
    </div>
  );
};

export default EmojiViewer; 