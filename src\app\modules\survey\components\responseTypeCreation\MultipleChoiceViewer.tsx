import React from "react";
import { useFormikContext } from "formik";
import { QuestionFormValues } from "../addQuestions/util/constant";
import { QuestionOptions } from "../../../../apis/type";
import { GoPlus } from "react-icons/go";
import FormErrorMessage from "../../../../component/form/FormErrorMessage";
import FormLabel from "../../../../component/form/FormLabel";
import { LuCircleMinus } from "react-icons/lu";

const MultipleChoiceViewer: React.FC = () => {
  const { values, setFieldValue } = useFormikContext<QuestionFormValues>();

  const handleAddOption = () => {
    const newOption: QuestionOptions = {
      optionText: "",
      optionValue: "",
      isTicketRequired: false,
    };
    setFieldValue("options", [...(values.options || []), newOption]);
  };

  const handleRemoveOption = (index: number) => {
    if (values.options && values.options.length > 1) {
      const newOptions = [...values.options];
      newOptions.splice(index, 1);
      setFieldValue("options", newOptions);
    }
  };

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...(values.options || [])];
    const currentOption = newOptions[index];

    if (typeof currentOption === 'string') {
      // Handle legacy string format
      newOptions[index] = {
        optionText: value,
        optionValue: value,
        isTicketRequired: false,
      };
    } else {
      // Handle QuestionOptions format
      newOptions[index] = {
        ...currentOption,
        optionText: value,
        optionValue: value,
      };
    }
    setFieldValue("options", newOptions);
  };

  return (
    <div className="multiple-choice-viewer">
      <div className="mb-3">
        <FormLabel className="form-label">Option</FormLabel>
        {values.options?.map((option, index) => {
          const optionText = typeof option === 'string' ? option : option.optionText;

          return (
            <div key={index}>
              <div className="d-flex align-items-center gap-2 mb-2">
                <input
                  type="text"
                  className="form-control"
                  value={optionText}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleOptionChange(index, e.target.value)
                  }
                  placeholder={`Option ${index + 1}`}
                />
                {values.options && values.options.length > 1 && (
                  <span role="button" className="mt-2 opacity-50" onClick={() => handleRemoveOption(index)}>
                    <LuCircleMinus size={30} />
                  </span>
                )}
              </div>
              <FormErrorMessage name="options" />
            </div>
          );
        })}
      </div>
      <button
        type="button"
        className="btn btn-light-primary btn-sm d-flex align-items-center gap-2"
        onClick={handleAddOption}
      >
        <GoPlus />
        Add Option
      </button>
    </div>
  );
};

export default MultipleChoiceViewer;
