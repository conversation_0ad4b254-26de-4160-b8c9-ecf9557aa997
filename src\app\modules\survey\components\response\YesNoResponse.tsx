import React, { useState, useEffect } from 'react';

interface YesNoResponseProps {
  options: { label: string; value: string }[];
  value: string;
  onChange: (value: string) => void;
}

const toTitleCase = (str: string) => str.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());

const YesNoResponse: React.FC<YesNoResponseProps> = ({ options, value, onChange }) => {
  const [selected, setSelected] = useState(value);

  useEffect(() => {
    setSelected(value);
  }, [value]);

  const handleChange = (val: string) => {
    setSelected(val);
    onChange(val);
  };

  return (
    <div style={{ display: 'flex', gap: 16 }}>
      {options.map((opt) => (
        <label key={opt.value} style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
          <input
            type="radio"
            name="yesno"
            checked={selected === opt.value}
            onChange={() => handleChange(opt.value)}
          />
          <span style={{ marginLeft: 8 }}>{toTitleCase(opt.label)}</span>
        </label>
      ))}
    </div>
  );
};

export default YesNoResponse; 