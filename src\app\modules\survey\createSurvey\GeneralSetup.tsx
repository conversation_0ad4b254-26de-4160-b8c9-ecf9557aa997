import React, { useEffect, useState } from "react";
import { FormikProps } from "formik";
import { Card, Col, Row } from "react-bootstrap";
import FormLabel from "../../../component/form/FormLabel";
import FormInput from "../../../component/form/FormInput";
import FormSelect from "../../../component/form/FormSelect";
import FormSwitch from "../../../component/form/FormSwitch";
import FormSurveyImageUpload from "../components/FormSurveyImageUpload";
import { SurveyFormValues, UserOption } from "./types";
import {
  useGetPropertiesListMutation,
  useGetUserDetailsByPropertyMutation,
} from "../../../apis/propertiesListAPI";
import {
  useGetAssignDepartmentListMutation,
  useGetUserDetailsByDepartmentMutation,
} from "../../../apis/departmentListAPI";
import { useSearchParams } from "react-router-dom";
import { SurveyType } from "../../../apis/type";
import useSurveyUtil from "../helper/useDetectSurvayType";

interface GeneralSetupProps {
  formik: FormikProps<SurveyFormValues>;
  viewOnly?: boolean;
  isPropertyBaseUser?: boolean;
  isMultiProperSelection?: boolean;
}

const GeneralSetup: React.FC<GeneralSetupProps> = ({
  formik,
  viewOnly = false,
  isPropertyBaseUser,
  isMultiProperSelection
}) => {
  const { setFieldValue, values } = formik;


  // API hooks
  const [
    getPropertiesList,
    { data: propertyList, isLoading: isPropertyLoading },
  ] = useGetPropertiesListMutation();

  const [
    getAssignDepartmentList,
    { data: departmentList, isLoading: isDepartmentLoading },
  ] = useGetAssignDepartmentListMutation();

  // State for user options
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);

  const handlePropertyChange = (option: any) => {
    if (!isPropertyBaseUser && option?.value) {
      setFieldValue("departmentIds", []);
      getAssignDepartmentList({ propertyid: option.value });
    }
  };

  useEffect(() => {
    getPropertiesList({});
  }, []);

  // Fetch departments if editing and user is department-based
  useEffect(() => {
    if (values.propertyIds && !isPropertyBaseUser) {
      const propertyId =
        typeof values.propertyIds === "string"
          ? values.propertyIds
          : values.propertyIds[0];
      if (propertyId) {
        getAssignDepartmentList({ propertyid: propertyId });
      }
    }
  }, [values.propertyIds, isPropertyBaseUser]);

  const [
    getUserDetailsByProperty,
    { data: propertyData, isLoading: isPropertyUserLoading },
  ] = useGetUserDetailsByPropertyMutation();

  const [
    getUserDetailsByDepartment,
    { data: departmentData, isLoading: isDepartmentUserLoading },
  ] = useGetUserDetailsByDepartmentMutation();

  // Fetch user list when propertyIds or departmentIds change
  useEffect(() => {
    // Clear surveyGoesTo selection when property or department changes
    if (isPropertyBaseUser) {
      if (values.propertyIds && values.propertyIds.length > 0) {
        const propertyId = Array.isArray(values.propertyIds)
          ? values.propertyIds[0] //TODO : Make it multiple
          : values.propertyIds;
        getUserDetailsByProperty({ propertyid: propertyId })
          .unwrap()
          .then((res: any) => {
            const options =
              res?.data?.map((user: any) => ({
                value: user.id,
                label: [user.firstname, user.lastname]
                  .filter(Boolean)
                  .join(" "),
              })) || [];
            setUserOptions(options);
          })
          .catch(() => setUserOptions([]));
      } else {
        setUserOptions([]);
      }
    } else {
      if (
        values.propertyIds &&
        values.propertyIds.length > 0 &&
        values.departmentIds &&
        values.departmentIds.length > 0
      ) {
        // const propertyId = Array.isArray(values.propertyIds)
        //   ? values.propertyIds[0]
        //   : values.propertyIds;
        getUserDetailsByDepartment({
          departmentid: values.departmentIds[0], //TODO : Make it multiple
        })
          .unwrap()
          .then((res: any) => {
            const options =
              res?.data?.map((user: any) => ({
                value: user.id,
                label: [user.firstname, user.lastname]
                  .filter(Boolean)
                  .join(" "),
              })) || [];
            setUserOptions(options);
          })
          .catch(() => setUserOptions([]));
      } else {
        setUserOptions([]);
      }
    }
  }, [values.propertyIds, values.departmentIds, isPropertyBaseUser]);

  return (
    <Card className="custom-card mb-5">
      <Card.Body className="p-4">
        <div style={{ maxWidth: 800 }}>
          <Row>
            <Col lg={12} className="mb-4">
              <FormLabel required={false}>Survey Background Image</FormLabel>
              <FormSurveyImageUpload name="surveyImage" disabled={viewOnly} />
            </Col>

            <Col lg={12} className="mb-4">
              <FormLabel>Survey Name</FormLabel>
              <FormInput
                name="surveyName"
                placeholder="Enter survey name"
                disabled={viewOnly}
                readOnly={viewOnly}
              />
            </Col>

            <Col lg={12} className="mb-4">
              <FormLabel>Survey Welcome Message</FormLabel>
              <FormInput
                name="surveyWelcomeMessage"
                placeholder="Enter welcome message"
                disabled={viewOnly}
                readOnly={viewOnly}
              />
            </Col>

            <Col lg={12} className="mb-4">
              <FormLabel>Property</FormLabel>
              <FormSelect
                isMulti={isMultiProperSelection}
                name="propertyIds"
                options={
                  propertyList?.data?.map?.(
                    (item: { propertyid: any; propertyname: string }) => ({
                      value: item.propertyid,
                      label: item.propertyname.trim(),
                    })
                  ) || []
                }
                onChange={handlePropertyChange}
                isLoading={isPropertyLoading}
                isDisabled={viewOnly}
                placeholder="Select property"
              />
            </Col>

            {!isPropertyBaseUser && (
              <Col lg={12} className="mb-4">
                <FormLabel>Department</FormLabel>
                <FormSelect
                  isMulti
                  name="departmentIds"
                  options={
                    departmentList?.data?.map(
                      (item: {
                        departmentId: any;
                        departmentName: string;
                      }) => ({
                        value: item.departmentId,
                        label: item.departmentName.trim(),
                      })
                    ) || []
                  }
                  isLoading={isDepartmentLoading}
                  isDisabled={viewOnly}
                  placeholder="Select department"
                />
              </Col>
            )}

            <Col lg={12} className="mb-4">
              <FormLabel>This survey goes to</FormLabel>
              <FormSelect
                isMulti
                name="surveyGoesTo"
                options={userOptions}
                isDisabled={viewOnly}
                placeholder="Select users"
                loadingMessage={() => "Loading users..."}
                isLoading={isPropertyUserLoading || isDepartmentUserLoading}
              />
            </Col>

            <Col lg={12} className="mb-4">
              <div className="d-flex align-items-center justify-content-between">
                <FormLabel required={false}>Survey Closing Message</FormLabel>
                <FormSwitch name="surveyClosingMessage" disabled={viewOnly} />
              </div>
            </Col>

            {values.surveyClosingMessage && (
              <Col lg={12} className="mb-4">
                <FormLabel>Closing Message</FormLabel>
                <FormInput
                  name="closingMessage"
                  placeholder="Enter closing message"
                  disabled={viewOnly}
                  readOnly={viewOnly}
                />
              </Col>
            )}

            <Col lg={12} className="">
              <div className="d-flex align-items-center justify-content-between">
                <FormLabel required={false}>Redirection URL</FormLabel>
                <FormSwitch name="redirectionUrl" disabled={viewOnly} />
              </div>
            </Col>

            {values.redirectionUrl && (
              <Col lg={12} className="mt-4">
                <FormLabel>Redirect URL</FormLabel>
                <FormInput
                  name="redirectUrl"
                  placeholder="Enter redirect URL (e.g., https://example.com)"
                  disabled={viewOnly}
                  readOnly={viewOnly}
                />
              </Col>
            )}
          </Row>
        </div>
      </Card.Body>
    </Card>
  );
};

export default GeneralSetup;
